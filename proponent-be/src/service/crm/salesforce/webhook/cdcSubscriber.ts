import { getSalesforceClient } from '../salesforceOauthServices';
import { supabaseAdminClient } from '@/db/supabaseClient';
import * as jsforce from 'jsforce';
import { Subscription } from 'faye';
import { handleContactCreation, handleCompanyCreation, handleContactPropertyChange, handleContactAssociationChange } from './salesforceWebhookServices';
import { SalesforceCDCEvent, SalesforceRawCDCEvent, SalesforceContactEvent, SalesforceAccountEvent, SalesforceAssociationEvent, SalesforceOpportunityContactRoleSubject } from './types';
import { crmAssociationsDBServices } from '@/service/crm-associations/association-tables';

export class SalesforceCDCSubscriber {
  // Reconnection configuration constants
  private static readonly MAX_RECONNECT_ATTEMPTS = 5;
  private static readonly INITIAL_RECONNECT_DELAY_MS = 5000;

  private activeSubscriptions: Map<string, Subscription> = new Map();
  private connectionHealth: Map<number, boolean> = new Map();
  private reconnectAttempts: Map<number, number> = new Map();

  async startCDCSubscription(tenantId: number) {
    try {
      console.log(`Starting CDC subscription for tenant ${tenantId}`);

      const salesforceClient = await getSalesforceClient({
        supabase: supabaseAdminClient,
        tenantId,
      });

      await this.subscribeToChangeEvents(salesforceClient, tenantId);

      this.connectionHealth.set(tenantId, true);
      this.reconnectAttempts.set(tenantId, 0);

      console.log(`CDC subscription started successfully for tenant ${tenantId}`);
    } catch (error) {
      console.error(`Failed to start CDC subscription for tenant ${tenantId}:`, error);
      this.connectionHealth.set(tenantId, false);

      setTimeout(() => this.attemptReconnection(tenantId), SalesforceCDCSubscriber.INITIAL_RECONNECT_DELAY_MS);
    }
  }

  private async subscribeToChangeEvents(client: jsforce.Connection, tenantId: number) {
    const channels = ['/data/ContactChangeEvent', '/data/AccountChangeEvent', '/data/OpportunityChangeEvent', '/data/OpportunityContactRoleChangeEvent'];

    for (const channel of channels) {
      try {
        const subscription = client.streaming.subscribe(channel, (message: SalesforceRawCDCEvent) => {
          console.log(`[Tenant ${tenantId}] Received CDC event on ${channel}:`, JSON.stringify(message, null, 2));
          this.processCDCEvent(message, tenantId);
        });

        this.activeSubscriptions.set(`${tenantId}-${channel}`, subscription);
        console.log(`[Tenant ${tenantId}] Successfully subscribed to ${channel}`);
      } catch (error) {
        console.error(`[Tenant ${tenantId}] Failed to subscribe to ${channel}:`, error);
        this.handleSubscriptionError(tenantId, channel, error);
      }
    }
  }

  async processCDCEvent(message: SalesforceRawCDCEvent, tenantId: number) {
    try {
      const transformedEvent = await this.transformCDCEvent(message, tenantId);
      this.processWebhookEvent(transformedEvent);
    } catch (error) {
      console.error(`[Tenant ${tenantId}] Error processing CDC event:`, error);
    }
  }

  private async transformCDCEvent(cdcEvent: SalesforceRawCDCEvent, tenantId: number): Promise<SalesforceCDCEvent> {
    const payload = cdcEvent.payload;
    const changeHeader = payload.ChangeEventHeader;

    const recordId = changeHeader.recordIds && changeHeader.recordIds.length > 0 ? changeHeader.recordIds[0] : payload.Id;

    // Validate that we have a valid recordId
    if (!recordId || recordId.trim() === '') {
      const errorMsg = `[Tenant ${tenantId}] Invalid or missing recordId in CDC event for ${changeHeader.entityName}. RecordIds: ${JSON.stringify(changeHeader.recordIds)}, Payload.Id: ${payload.Id}`;
      console.error(errorMsg);
      throw new Error(errorMsg);
    }

    // Extract all fields except ChangeEventHeader
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
    const { ChangeEventHeader, ...fieldValues } = payload;

    // Flatten Salesforce Name object structure for Contact entities
    const processedFieldValues =
      changeHeader.entityName === 'Contact' && fieldValues.Name && typeof fieldValues.Name === 'object'
        ? {
            ...fieldValues,
            FirstName: fieldValues.Name.FirstName,
            LastName: fieldValues.Name.LastName,
            Salutation: fieldValues.Name.Salutation,
          }
        : fieldValues;

    return {
      eventType: this.mapChangeType(changeHeader.changeType),
      subject: {
        Id: recordId,
        attributes: {
          type: changeHeader.entityName,
        },
        ...processedFieldValues,
      },
      tenantId,
      changeEventHeader: {
        entityName: changeHeader.entityName,
        changeType: changeHeader.changeType,
        changedFields: changeHeader.changedFields || [],
        recordIds: changeHeader.recordIds || [],
        transactionKey: changeHeader.transactionKey,
        commitTimestamp: changeHeader.commitTimestamp,
        commitUser: changeHeader.commitUser,
        commitNumber: changeHeader.commitNumber,
        sequenceNumber: changeHeader.sequenceNumber,
        nulledFields: changeHeader.nulledFields || [],
        changeOrigin: changeHeader.changeOrigin,
      },
    };
  }

  private mapChangeType(changeType: string): 'created' | 'updated' | 'deleted' {
    switch (changeType) {
      case 'CREATE':
        return 'created';
      case 'UPDATE':
        return 'updated';
      case 'DELETE':
        return 'deleted';
      default:
        return changeType.toLowerCase() as 'created' | 'updated' | 'deleted';
    }
  }

  private async processWebhookEvent(event: SalesforceCDCEvent) {
    try {
      console.log(`[Tenant ${event.tenantId}] Processing CDC event: ${event.eventType} for ${event.subject?.attributes?.type}`);

      const entityType = event.subject?.attributes?.type;

      switch (event.eventType) {
        case 'created':
          if (entityType === 'Contact') await handleContactCreation(event as SalesforceContactEvent);
          else if (entityType === 'Account') await handleCompanyCreation(event as SalesforceAccountEvent);
          else if (entityType === 'OpportunityContactRole') await this.handleOpportunityContactRoleChange(event, false);

          break;
        case 'updated':
          if (entityType === 'Contact') await handleContactPropertyChange(event as SalesforceContactEvent);
          else if (entityType === 'OpportunityContactRole') await this.handleOpportunityContactRoleChange(event, false);
          else if (entityType === 'Opportunity') await this.handleOpportunityChange(event);

          break;
        case 'deleted':
          if (entityType === 'OpportunityContactRole') await this.handleOpportunityContactRoleChange(event, true);
          else console.log(`[Tenant ${event.tenantId}] Handling deletion for ${entityType}`);

          break;
        default:
          console.log(`[Tenant ${event.tenantId}] Unhandled CDC event: ${event.eventType} for ${entityType}`);
      }
    } catch (error) {
      console.error(`[Tenant ${event.tenantId}] Error processing CDC event ${event.eventType}:`, error);
    }
  }

  private async handleOpportunityContactRoleChange(event: SalesforceCDCEvent, isRemoved: boolean) {
    try {
      const opportunityContactRoleSubject = event.subject as SalesforceOpportunityContactRoleSubject;

      const associationEvent: SalesforceAssociationEvent = {
        ...event,
        fromObjectId: opportunityContactRoleSubject.ContactId,
        toObjectId: opportunityContactRoleSubject.OpportunityId,
        relationshipName: 'Contact_Opportunity',
        eventType: isRemoved ? 'deleted' : event.eventType,
      } as SalesforceAssociationEvent;

      await handleContactAssociationChange(associationEvent);
    } catch (error) {
      console.error(`[Tenant ${event.tenantId}] Error handling OpportunityContactRole change:`, error);
    }
  }

  private async handleOpportunityChange(event: SalesforceCDCEvent) {
    try {
      const { tenantId, changeEventHeader } = event;
      const opportunityId = event.subject.Id;

      // Check if ContactId field was changed
      if (!changeEventHeader.changedFields.includes('ContactId')) {
        console.log(`[Tenant ${tenantId}] Opportunity ${opportunityId} updated but ContactId not changed. Skipping.`);
        return;
      }

      console.log(`[Tenant ${tenantId}] Opportunity ${opportunityId} ContactId changed. Syncing contact associations.`);

      // Sync contact associations for this opportunity
      await this.syncOpportunityContactAssociations(tenantId, opportunityId);
    } catch (error) {
      console.error(`[Tenant ${event.tenantId}] Error handling Opportunity change:`, error);
    }
  }

  private async syncOpportunityContactAssociations(tenantId: number, opportunityId: string) {
    try {
      // Get Salesforce client
      const salesforceClient = await getSalesforceClient({
        supabase: supabaseAdminClient,
        tenantId,
      });

      // Query current OpportunityContactRole records from Salesforce
      const result = await salesforceClient.query(`
        SELECT ContactId, OpportunityId, IsPrimary, Role
        FROM OpportunityContactRole
        WHERE OpportunityId = '${opportunityId}' AND IsDeleted = false
      `);

      const currentSalesforceAssociations: string[] = result.records.map((record: any) => record.ContactId);

      console.log(`[Tenant ${tenantId}] Found ${currentSalesforceAssociations.length} contact associations in Salesforce for opportunity ${opportunityId}`);

      // Get current associations from our local database
      const localAssociations = await crmAssociationsDBServices.contactDeal.getContactDealAssociations({
        supabase: supabaseAdminClient,
        tenantId,
        dealCrmId: opportunityId,
      });

      const currentLocalAssociations: string[] = localAssociations.map((assoc: any) => assoc.contactCrmId);

      console.log(`[Tenant ${tenantId}] Found ${currentLocalAssociations.length} contact associations in local DB for opportunity ${opportunityId}`);

      // Find associations to add (in Salesforce but not in local DB)
      const associationsToAdd = currentSalesforceAssociations.filter(
        (contactId: string) => !currentLocalAssociations.includes(contactId)
      );

      // Find associations to remove (in local DB but not in Salesforce)
      const associationsToRemove = currentLocalAssociations.filter(
        (contactId: string) => !currentSalesforceAssociations.includes(contactId)
      );

      console.log(`[Tenant ${tenantId}] Syncing opportunity ${opportunityId}: adding ${associationsToAdd.length}, removing ${associationsToRemove.length} associations`);

      // Add new associations
      for (const contactId of associationsToAdd) {
        await handleContactAssociationChange({
          eventType: 'created',
          fromObjectId: contactId,
          toObjectId: opportunityId,
          relationshipName: 'Contact_Opportunity',
          tenantId,
        } as SalesforceAssociationEvent);
      }

      // Remove old associations
      for (const contactId of associationsToRemove) {
        await handleContactAssociationChange({
          eventType: 'deleted',
          fromObjectId: contactId,
          toObjectId: opportunityId,
          relationshipName: 'Contact_Opportunity',
          tenantId,
        } as SalesforceAssociationEvent);
      }

      console.log(`[Tenant ${tenantId}] Successfully synced contact associations for opportunity ${opportunityId}`);
    } catch (error) {
      console.error(`[Tenant ${tenantId}] Error syncing opportunity contact associations for ${opportunityId}:`, error);
    }
  }

  private handleSubscriptionError(tenantId: number, channel: string, error: any) {
    console.error(`[Tenant ${tenantId}] Subscription error on ${channel}:`, error);
    this.connectionHealth.set(tenantId, false);

    this.activeSubscriptions.delete(`${tenantId}-${channel}`);

    setTimeout(() => this.attemptReconnection(tenantId), SalesforceCDCSubscriber.INITIAL_RECONNECT_DELAY_MS);
  }

  private async attemptReconnection(tenantId: number) {
    const attempts = this.reconnectAttempts.get(tenantId) || 0;

    if (attempts >= SalesforceCDCSubscriber.MAX_RECONNECT_ATTEMPTS) {
      console.error(`[Tenant ${tenantId}] Max reconnection attempts reached. Giving up.`);
      return;
    }

    console.log(`[Tenant ${tenantId}] Attempting reconnection (attempt ${attempts + 1}/${SalesforceCDCSubscriber.MAX_RECONNECT_ATTEMPTS})`);
    this.reconnectAttempts.set(tenantId, attempts + 1);

    try {
      await this.stopCDCSubscription(tenantId);
      await this.startCDCSubscription(tenantId);
    } catch (error) {
      console.error(`[Tenant ${tenantId}] Reconnection attempt failed:`, error);

      const delay = SalesforceCDCSubscriber.INITIAL_RECONNECT_DELAY_MS * Math.pow(2, attempts);
      setTimeout(() => this.attemptReconnection(tenantId), delay);
    }
  }

  async stopCDCSubscription(tenantId: number) {
    console.log(`[Tenant ${tenantId}] Stopping CDC subscriptions`);

    for (const [key, subscription] of this.activeSubscriptions.entries()) {
      if (key.startsWith(`${tenantId}-`)) {
        try {
          subscription.cancel();
          console.log(`[Tenant ${tenantId}] Cancelled subscription: ${key}`);
        } catch (error) {
          console.error(`[Tenant ${tenantId}] Error cancelling subscription ${key}:`, error);
        }
        this.activeSubscriptions.delete(key);
      }
    }

    this.connectionHealth.set(tenantId, false);
    this.reconnectAttempts.delete(tenantId);
  }

  isHealthy(tenantId: number): boolean {
    return this.connectionHealth.get(tenantId) || false;
  }

  getActiveSubscriptions(): string[] {
    return Array.from(this.activeSubscriptions.keys());
  }

  async stopAllSubscriptions() {
    console.log('Stopping all CDC subscriptions');

    for (const [key, subscription] of this.activeSubscriptions.entries()) {
      try {
        subscription.cancel();
        console.log(`Cancelled subscription: ${key}`);
      } catch (error) {
        console.error(`Error cancelling subscription ${key}:`, error);
      }
    }

    this.activeSubscriptions.clear();
    this.connectionHealth.clear();
    this.reconnectAttempts.clear();
  }
}

export const cdcSubscriber = new SalesforceCDCSubscriber();
