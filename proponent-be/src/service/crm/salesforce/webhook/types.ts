export interface SalesforceChangeEventHeader {
  entityName: string;
  changeType: 'CREATE' | 'UPDATE' | 'DELETE' | 'UNDELETE';
  changedFields: string[];
  recordIds: string[];
  transactionKey: string;
  commitTimestamp: number;
  commitUser: string;
  commitNumber: number;
  sequenceNumber?: number;
  nulledFields?: string[];
  changeOrigin?: string;
}

export interface SalesforceName {
  FirstName?: string;
  LastName?: string;
  Salutation?: string;
}

export interface SalesforceContactSubject {
  Id: string;
  attributes: {
    type: string;
  };
  Email?: string;
  FirstName?: string;
  LastName?: string;
  Salutation?: string;
  Title?: string;
  Department?: string;
  Phone?: string;
  MobilePhone?: string;
  AccountId?: string;
  OwnerId?: string;
  CreatedById?: string;
  CreatedDate?: string;
  LastModifiedById?: string;
  LastModifiedDate?: string;
  CleanStatus?: string;
  [key: string]: any;
}

export interface SalesforceAccountSubject {
  Id: string;
  attributes: {
    type: string;
  };
  Name?: string;
  Industry?: string;
  Type?: string;
  Website?: string;
  Phone?: string;
  BillingCity?: string;
  BillingState?: string;
  BillingCountry?: string;
  AnnualRevenue?: number;
  NumberOfEmployees?: number;
  OwnerId?: string;
  CreatedById?: string;
  CreatedDate?: string;
  LastModifiedById?: string;
  LastModifiedDate?: string;
  [key: string]: any;
}

export interface SalesforceOpportunitySubject {
  Id: string;
  attributes: {
    type: string;
  };
  Name?: string;
  AccountId?: string;
  ContactId?: string;
  Amount?: number;
  CloseDate?: string;
  StageName?: string;
  Probability?: number;
  OwnerId?: string;
  CreatedById?: string;
  CreatedDate?: string;
  LastModifiedById?: string;
  LastModifiedDate?: string;
  [key: string]: any;
}

export interface SalesforceOpportunityContactRoleSubject {
  Id: string;
  attributes: {
    type: string;
  };
  ContactId: string;
  OpportunityId: string;
  Role?: string;
  IsPrimary?: boolean;
  CreatedById?: string;
  CreatedDate?: string;
  LastModifiedById?: string;
  LastModifiedDate?: string;
  [key: string]: any;
}

export type SalesforceSubject = SalesforceContactSubject | SalesforceAccountSubject | SalesforceOpportunitySubject | SalesforceOpportunityContactRoleSubject;

export interface SalesforceCDCEvent {
  eventType: 'created' | 'updated' | 'deleted';
  subject: SalesforceSubject;
  tenantId: number;
  changeEventHeader: SalesforceChangeEventHeader;
}

export interface SalesforceContactEvent extends SalesforceCDCEvent {
  subject: SalesforceContactSubject;
}

export interface SalesforceAccountEvent extends SalesforceCDCEvent {
  subject: SalesforceAccountSubject;
}

export interface SalesforceOpportunityEvent extends SalesforceCDCEvent {
  subject: SalesforceOpportunitySubject;
}

export interface SalesforceAssociationEvent extends SalesforceCDCEvent {
  fromObjectId: string;
  toObjectId: string;
  relationshipName: 'Contact_Opportunity' | 'Contact_Account' | 'Account_Opportunity';
}

// Raw CDC event structure from Salesforce
export interface SalesforceRawCDCPayload {
  ChangeEventHeader: SalesforceChangeEventHeader;
  Id?: string;
  Name?: SalesforceName | string;
  Email?: string;
  Title?: string;
  Department?: string;
  ContactId?: string;
  OpportunityId?: string;
  AccountId?: string;
  [key: string]: any;
}

export interface SalesforceRawCDCEvent {
  schema?: string;
  payload: SalesforceRawCDCPayload;
  event?: {
    replayId: number;
  };
}
